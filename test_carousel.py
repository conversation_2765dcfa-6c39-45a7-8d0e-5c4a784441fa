#!/usr/bin/env python3
"""
Test script for the Flet Carousel Slider extension.
This script verifies that the extension can be imported and basic functionality works.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """Test that the carousel slider can be imported successfully."""
    try:
        from flet_carousel_slider import (
            FletCarouselSlider,
            CarouselPageChangedReason,
            CenterPageEnlargeStrategy,
            ScrollDirection,
        )
        print("✅ Import test passed - All classes imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_instantiation():
    """Test that the carousel slider can be instantiated."""
    try:
        from flet_carousel_slider import FletCarouselSlider, ScrollDirection, CenterPageEnlargeStrategy
        import flet as ft
        
        # Create some dummy items
        items = [
            ft.Container(content=ft.Text("Item 1")),
            ft.Container(content=ft.Text("Item 2")),
            ft.Container(content=ft.Text("Item 3")),
        ]
        
        # Test basic instantiation
        carousel = FletCarouselSlider(
            items=items,
            height=300,
            auto_play=True,
            scroll_direction=ScrollDirection.HORIZONTAL,
            enlarge_strategy=CenterPageEnlargeStrategy.SCALE,
        )
        
        # Test that properties are set correctly
        assert carousel.height == 300
        assert carousel.auto_play == True
        assert len(carousel.items) == 3
        
        print("✅ Instantiation test passed - Carousel created successfully")
        return True
    except Exception as e:
        print(f"❌ Instantiation test failed: {e}")
        return False

def test_properties():
    """Test that properties can be set and retrieved."""
    try:
        from flet_carousel_slider import FletCarouselSlider
        import flet as ft
        
        carousel = FletCarouselSlider()
        
        # Test setting properties
        carousel.height = 400
        carousel.auto_play = True
        carousel.auto_play_interval = 5000
        carousel.viewport_fraction = 0.9
        
        # Test that properties are accessible (they should not raise errors)
        _ = carousel.height
        _ = carousel.auto_play
        _ = carousel.auto_play_interval
        _ = carousel.viewport_fraction
        
        print("✅ Properties test passed - All properties accessible")
        return True
    except Exception as e:
        print(f"❌ Properties test failed: {e}")
        return False

def test_methods():
    """Test that methods can be called without errors."""
    try:
        from flet_carousel_slider import FletCarouselSlider
        
        carousel = FletCarouselSlider()
        
        # Test that methods exist and are callable
        assert callable(carousel.next_page)
        assert callable(carousel.previous_page)
        assert callable(carousel.jump_to_page)
        assert callable(carousel.animate_to_page)
        assert callable(carousel.get_current_page)
        assert callable(carousel.start_auto_play)
        assert callable(carousel.stop_auto_play)
        
        print("✅ Methods test passed - All methods are callable")
        return True
    except Exception as e:
        print(f"❌ Methods test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Flet Carousel Slider Extension")
    print("=" * 50)
    
    tests = [
        test_import,
        test_instantiation,
        test_properties,
        test_methods,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The extension is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
