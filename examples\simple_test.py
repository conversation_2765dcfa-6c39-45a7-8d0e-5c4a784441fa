#!/usr/bin/env python3

import flet as ft
from src.flet_carousel_slider import FletCarouselSlider

def main(page: ft.Page):
    page.title = "Simple Carousel Test"
    page.padding = 20

    # Create simple carousel items
    items = []
    for i in range(5):
        item = ft.Container(
            content=ft.Text(
                f"Slide {i+1}", 
                size=30, 
                color=ft.colors.WHITE,
                weight=ft.FontWeight.BOLD
            ),
            bgcolor=ft.colors.BLUE_400 if i % 2 == 0 else ft.colors.RED_400,
            border_radius=10,
            padding=40,
            alignment=ft.alignment.center,
        )
        items.append(item)

    # Create carousel
    carousel = FletCarouselSlider(
        items=items,
        height=200,
        auto_play=True,
        auto_play_interval=2000,  # 2 seconds
        enlarge_center_page=True,
        viewport_fraction=0.8,
    )

    # Add to page
    page.add(
        ft.Column([
            ft.Text("Simple Carousel Test", size=24, weight=ft.FontWeight.BOLD),
            carousel,
        ], spacing=20)
    )

if __name__ == "__main__":
    ft.app(target=main)
