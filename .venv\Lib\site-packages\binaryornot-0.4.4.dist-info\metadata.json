{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: BSD License", "Natural Language :: English", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/audreyr/binaryornot"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "keywords": ["<PERSON><PERSON><PERSON>"], "license": "BSD", "metadata_version": "2.0", "name": "<PERSON><PERSON><PERSON>", "run_requires": [{"requires": ["chardet (>=3.0.2)"]}], "summary": "Ultra-lightweight pure Python package to check if a file is binary or text.", "version": "0.4.4"}