Metadata-Version: 2.4
Name: types-python-dateutil
Version: 2.9.0.20250708
Summary: Typing stubs for python-dateutil
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/python-dateutil.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: license-file

## Typing stubs for python-dateutil

This is a [PEP 561](https://peps.python.org/pep-0561/)
type stub package for the [`python-dateutil`](https://github.com/dateutil/dateutil) package.
It can be used by type-checking tools like
[mypy](https://github.com/python/mypy/),
[pyright](https://github.com/microsoft/pyright),
[pytype](https://github.com/google/pytype/),
[Pyre](https://pyre-check.org/),
PyCharm, etc. to check code that uses `python-dateutil`. This version of
`types-python-dateutil` aims to provide accurate annotations for
`python-dateutil==2.9.*`.

This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/python-dateutil`](https://github.com/python/typeshed/tree/main/stubs/python-dateutil)
directory.

This package was tested with
mypy 1.16.1,
pyright 1.1.400,
and pytype 2024.10.11.
It was generated from typeshed commit
[`f4bf1d90a64d3f885ea30c642fe5373e2d19c344`](https://github.com/python/typeshed/commit/f4bf1d90a64d3f885ea30c642fe5373e2d19c344).
