import 'package:flet/flet.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'dart:convert';

class FletCarouselSliderControl extends StatefulWidget {
  final Control? parent;
  final Control control;
  final List<Control> children;
  final bool parentDisabled;
  final bool? parentAdaptive;
  final FletControlBackend backend;

  const FletCarouselSliderControl({
    super.key,
    required this.parent,
    required this.control,
    required this.children,
    required this.parentDisabled,
    required this.parentAdaptive,
    required this.backend,
  });

  @override
  State<FletCarouselSliderControl> createState() =>
      _FletCarouselSliderControlState();
}

class _FletCarouselSliderControlState extends State<FletCarouselSliderControl> {
  late CarouselSliderController _carouselController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _carouselController = CarouselSliderController();
    widget.backend.subscribeMethods(widget.control.id, _onMethodCall);
    _currentPage = widget.control.attrInt("initialPage", 0) ?? 0;
  }

  @override
  void dispose() {
    widget.backend.unsubscribeMethods(widget.control.id);
    super.dispose();
  }

  Future<String?> _onMethodCall(String methodName, Map<String, String> args) async {
    switch (methodName) {
      case "next_page":
        final int duration = int.tryParse(args["duration"] ?? "300") ?? 300;
        final String curve = args["curve"] ?? "linear";
        _carouselController.nextPage(
          duration: Duration(milliseconds: duration),
          curve: _getCurve(curve),
        );
        return null;

      case "previous_page":
        final int duration = int.tryParse(args["duration"] ?? "300") ?? 300;
        final String curve = args["curve"] ?? "linear";
        _carouselController.previousPage(
          duration: Duration(milliseconds: duration),
          curve: _getCurve(curve),
        );
        return null;

      case "jump_to_page":
        final int page = int.tryParse(args["page"] ?? "0") ?? 0;
        _carouselController.jumpToPage(page);
        return null;

      case "animate_to_page":
        final int page = int.tryParse(args["page"] ?? "0") ?? 0;
        final int duration = int.tryParse(args["duration"] ?? "300") ?? 300;
        final String curve = args["curve"] ?? "linear";
        _carouselController.animateToPage(
          page,
          duration: Duration(milliseconds: duration),
          curve: _getCurve(curve),
        );
        return null;

      case "get_current_page":
        return _currentPage.toString();

      case "start_auto_play":
        // Note: CarouselSlider doesn't have direct start/stop methods
        // This would require rebuilding the widget with autoPlay: true
        return null;

      case "stop_auto_play":
        // Note: CarouselSlider doesn't have direct start/stop methods
        // This would require rebuilding the widget with autoPlay: false
        return null;

      default:
        return null;
    }
  }

  Curve _getCurve(String curveName) {
    switch (curveName.toLowerCase()) {
      case "linear":
        return Curves.linear;
      case "ease":
        return Curves.ease;
      case "easein":
        return Curves.easeIn;
      case "easeout":
        return Curves.easeOut;
      case "easeinout":
        return Curves.easeInOut;
      case "fastoutslowIn":
        return Curves.fastOutSlowIn;
      case "slowmiddle":
        return Curves.slowMiddle;
      case "bouncein":
        return Curves.bounceIn;
      case "bounceout":
        return Curves.bounceOut;
      case "bounceinout":
        return Curves.bounceInOut;
      case "elasticin":
        return Curves.elasticIn;
      case "elasticout":
        return Curves.elasticOut;
      case "elasticinout":
        return Curves.elasticInOut;
      default:
        return Curves.linear;
    }
  }

  CenterPageEnlargeStrategy _getEnlargeStrategy(String? strategy) {
    switch (strategy?.toLowerCase()) {
      case "scale":
        return CenterPageEnlargeStrategy.scale;
      case "height":
        return CenterPageEnlargeStrategy.height;
      case "zoom":
        return CenterPageEnlargeStrategy.zoom;
      default:
        return CenterPageEnlargeStrategy.scale;
    }
  }

  Axis _getScrollDirection(String? direction) {
    switch (direction?.toLowerCase()) {
      case "vertical":
        return Axis.vertical;
      case "horizontal":
      default:
        return Axis.horizontal;
    }
  }

  void _onPageChanged(int index, CarouselPageChangedReason reason) {
    setState(() {
      _currentPage = index;
    });

    // Trigger page changed event
    final eventData = {
      "index": index,
      "reason": reason.toString().split('.').last,
    };

    widget.backend.triggerControlEvent(
      widget.control.id,
      "page_changed",
      json.encode(eventData),
    );
  }

  void _onScrolled(double? offset) {
    // Trigger scrolled event
    final eventData = {
      "offset": offset ?? 0.0,
    };

    widget.backend.triggerControlEvent(
      widget.control.id,
      "scrolled",
      json.encode(eventData),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get carousel items from children
    final itemControls = widget.children.where((c) => c.name?.startsWith("item_") == true && c.isVisible);

    bool disabled = widget.control.isDisabled || widget.parentDisabled;
    bool? adaptive = widget.control.attrBool("adaptive") ?? widget.parentAdaptive;

    // Build carousel items
    List<Widget> carouselItems = itemControls.map((itemControl) {
      return createControl(
        widget.control,
        itemControl.id,
        disabled,
        parentAdaptive: adaptive,
      );
    }).toList();

    // If no items provided, show placeholder
    if (carouselItems.isEmpty) {
      carouselItems = [
        Container(
          child: const Center(
            child: Text(
              "No items provided",
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ),
        ),
      ];
    }

    // Parse carousel options
    final double? height = widget.control.attrDouble("height");
    final double aspectRatio = widget.control.attrDouble("aspectRatio", 16/9) ?? 16/9;
    final double viewportFraction = widget.control.attrDouble("viewportFraction", 0.8) ?? 0.8;
    final int initialPage = widget.control.attrInt("initialPage", 0) ?? 0;
    final bool enableInfiniteScroll = widget.control.attrBool("enableInfiniteScroll", true) ?? true;
    final bool animateToClosest = widget.control.attrBool("animateToClosest", true) ?? true;
    final bool reverse = widget.control.attrBool("reverse", false) ?? false;
    final bool autoPlay = widget.control.attrBool("autoPlay", false) ?? false;
    final int autoPlayInterval = widget.control.attrInt("autoPlayInterval", 4000) ?? 4000;
    final int autoPlayAnimationDuration = widget.control.attrInt("autoPlayAnimationDuration", 800) ?? 800;
    final String autoPlayCurve = widget.control.attrString("autoPlayCurve", "fastOutSlowIn") ?? "fastOutSlowIn";
    final bool enlargeCenterPage = widget.control.attrBool("enlargeCenterPage", false) ?? false;
    final double enlargeFactor = widget.control.attrDouble("enlargeFactor", 0.3) ?? 0.3;
    final String enlargeStrategy = widget.control.attrString("enlargeStrategy", "scale") ?? "scale";
    final bool pageSnapping = widget.control.attrBool("pageSnapping", true) ?? true;
    final String scrollDirection = widget.control.attrString("scrollDirection", "horizontal") ?? "horizontal";
    final bool pauseAutoPlayOnTouch = widget.control.attrBool("pauseAutoPlayOnTouch", true) ?? true;
    final bool pauseAutoPlayOnManualNavigate = widget.control.attrBool("pauseAutoPlayOnManualNavigate", true) ?? true;
    final bool pauseAutoPlayInFiniteScroll = widget.control.attrBool("pauseAutoPlayInFiniteScroll", false) ?? false;
    final bool disableCenter = widget.control.attrBool("disableCenter", false) ?? false;
    final bool padEnds = widget.control.attrBool("padEnds", true) ?? true;

    // Create CarouselOptions
    final CarouselOptions options = CarouselOptions(
      height: height,
      aspectRatio: aspectRatio,
      viewportFraction: viewportFraction,
      initialPage: initialPage,
      enableInfiniteScroll: enableInfiniteScroll,
      animateToClosest: animateToClosest,
      reverse: reverse,
      autoPlay: autoPlay,
      autoPlayInterval: Duration(milliseconds: autoPlayInterval),
      autoPlayAnimationDuration: Duration(milliseconds: autoPlayAnimationDuration),
      autoPlayCurve: _getCurve(autoPlayCurve),
      enlargeCenterPage: enlargeCenterPage,
      enlargeFactor: enlargeFactor,
      enlargeStrategy: _getEnlargeStrategy(enlargeStrategy),
      pageSnapping: pageSnapping,
      scrollDirection: _getScrollDirection(scrollDirection),
      pauseAutoPlayOnTouch: pauseAutoPlayOnTouch,
      pauseAutoPlayOnManualNavigate: pauseAutoPlayOnManualNavigate,
      pauseAutoPlayInFiniteScroll: pauseAutoPlayInFiniteScroll,
      disableCenter: disableCenter,
      padEnds: padEnds,
      onPageChanged: _onPageChanged,
      onScrolled: _onScrolled,
    );

    // Create the CarouselSlider widget
    Widget carouselSlider = CarouselSlider(
      items: carouselItems,
      carouselController: _carouselController,
      options: options,
    );

    return constrainedControl(
      context,
      carouselSlider,
      widget.parent,
      widget.control,
    );
  }
}
