# Flet Carousel Slider Extension

A Flet extension that wraps <PERSON><PERSON><PERSON>'s `carousel_slider` package, providing a powerful and customizable carousel widget for your Flet applications.

## Features

- ✨ **Infinite scroll** - Seamlessly loop through items
- 🎯 **Custom child widgets** - Use any Flet control as carousel items
- ⏰ **Auto play** - Automatic sliding with customizable intervals
- 🎮 **Manual control** - Programmatic navigation with smooth animations
- 📱 **Responsive** - Adapts to different screen sizes
- 🎨 **Highly customizable** - Extensive styling and behavior options

## Installation

1. Add the extension to your project by copying the `src/flet_carousel_slider` directory
2. Install the Flutter dependencies by running `flutter pub get` in the `src/flutter/flet_carousel_slider` directory

## Quick Start

```python
import flet as ft
from src.flet_carousel_slider import FletCarouselSlider

def main(page: ft.Page):
    # Create carousel items
    items = [
        ft.Container(
            content=ft.Text(f"Slide {i}", size=24, color=ft.colors.WHITE),
            bgcolor=ft.colors.BLUE,
            border_radius=10,
            padding=20,
        ) for i in range(1, 6)
    ]
    
    # Create carousel
    carousel = FletCarouselSlider(
        items=items,
        height=300,
        auto_play=True,
        auto_play_interval=3000,
        enlarge_center_page=True,
    )
    
    page.add(carousel)

ft.app(target=main)
```

## Properties

### Basic Properties

- **`items`** (`List[Control]`): List of widgets to display in the carousel
- **`height`** (`float`, optional): Fixed height for the carousel
- **`aspect_ratio`** (`float`, default: `16/9`): Aspect ratio when height is not specified

### Layout Properties

- **`viewport_fraction`** (`float`, default: `0.8`): Fraction of viewport each page occupies
- **`initial_page`** (`int`, default: `0`): Initial page to display
- **`scroll_direction`** (`ScrollDirection`, default: `HORIZONTAL`): Scroll axis

### Auto Play Properties

- **`auto_play`** (`bool`, default: `False`): Enable automatic sliding
- **`auto_play_interval`** (`int`, default: `4000`): Interval between slides (milliseconds)
- **`auto_play_animation_duration`** (`int`, default: `800`): Animation duration (milliseconds)
- **`auto_play_curve`** (`str`, default: `"fastOutSlowIn"`): Animation curve

### Visual Enhancement Properties

- **`enlarge_center_page`** (`bool`, default: `False`): Make center page larger
- **`enlarge_factor`** (`float`, default: `0.3`): Scale factor for side pages
- **`enlarge_strategy`** (`CenterPageEnlargeStrategy`, default: `SCALE`): Enlargement method

### Behavior Properties

- **`enable_infinite_scroll`** (`bool`, default: `True`): Enable infinite scrolling
- **`page_snapping`** (`bool`, default: `True`): Enable page snapping
- **`reverse`** (`bool`, default: `False`): Reverse item order

## Events

### `on_page_changed`
Triggered when the current page changes.

```python
def on_page_changed(e):
    data = e.data  # {"index": int, "reason": str}
    print(f"Page changed to {data['index']}")

carousel.on_page_changed = on_page_changed
```

### `on_scrolled`
Triggered during scrolling.

```python
def on_scrolled(e):
    data = e.data  # {"offset": float}
    print(f"Scrolled to offset {data['offset']}")

carousel.on_scrolled = on_scrolled
```

## Methods

### Navigation Methods

```python
# Navigate to next page
carousel.next_page(duration=300, curve="easeInOut")

# Navigate to previous page
carousel.previous_page(duration=300, curve="linear")

# Jump to specific page (no animation)
carousel.jump_to_page(2)

# Animate to specific page
carousel.animate_to_page(2, duration=500, curve="bounceOut")
```

### Control Methods

```python
# Get current page index
current_page = carousel.get_current_page()

# Control auto play
carousel.start_auto_play()
carousel.stop_auto_play()
```

## Enums

### `ScrollDirection`
- `HORIZONTAL`: Horizontal scrolling
- `VERTICAL`: Vertical scrolling

### `CenterPageEnlargeStrategy`
- `SCALE`: Scale the center page
- `HEIGHT`: Increase height of center page
- `ZOOM`: Zoom effect on center page

### `CarouselPageChangedReason`
- `CONTROLLER`: Changed via controller
- `MANUAL`: Changed by user interaction
- `TIMED`: Changed by auto play

## Examples

See `examples/carousel_example.py` for a comprehensive example demonstrating all features.

## License

This extension is provided as-is for educational and development purposes.
