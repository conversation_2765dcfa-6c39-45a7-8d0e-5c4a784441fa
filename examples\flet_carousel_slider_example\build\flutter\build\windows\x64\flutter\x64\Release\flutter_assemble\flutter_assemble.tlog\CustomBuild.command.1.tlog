^C:\USERS\<USER>\DESKTOP\FLET\APP_VIEW\APP_VIEW\EXAMPLES\FLET_CAROUSEL_SLIDER_EXAMPLE\BUILD\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\147407179C8B58EC49A311BF0CA0CDCB\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\Users\<USER>\flutter\3.29.2 PROJECT_DIR=C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter FLUTTER_ROOT=C:\Users\<USER>\flutter\3.29.2 FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter FLUTTER_TARGET=lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter\.dart_tool\package_config.json C:/Users/<USER>/flutter/3.29.2/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\FLET\APP_VIEW\APP_VIEW\EXAMPLES\FLET_CAROUSEL_SLIDER_EXAMPLE\BUILD\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\88EC16C5817F76F2253C834CD407A70E\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DESKTOP\FLET\APP_VIEW\APP_VIEW\EXAMPLES\FLET_CAROUSEL_SLIDER_EXAMPLE\BUILD\FLUTTER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/flet/app_view/app_view/examples/flet_carousel_slider_example/build/flutter/windows -BC:/Users/<USER>/Desktop/flet/app_view/app_view/examples/flet_carousel_slider_example/build/flutter/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/flet/app_view/app_view/examples/flet_carousel_slider_example/build/flutter/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
