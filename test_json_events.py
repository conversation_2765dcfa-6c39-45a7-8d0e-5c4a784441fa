#!/usr/bin/env python3
"""
Test script to verify JSON event handling in the carousel slider.
"""

import sys
import os
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_json_event_handling():
    """Test that JSON events are properly decoded."""
    try:
        from flet_carousel_slider import FletCarouselSlider
        
        # Track received events
        received_events = []
        
        def on_page_changed(e):
            received_events.append(('page_changed', e.data))
        
        def on_scrolled(e):
            received_events.append(('scrolled', e.data))
        
        # Create carousel with event handlers
        carousel = FletCarouselSlider(
            on_page_changed=on_page_changed,
            on_scrolled=on_scrolled
        )
        
        # Simulate JSON event data (like what would come from Dart)
        class MockEvent:
            def __init__(self, data):
                self.data = data
        
        # Test page changed event
        page_changed_json = json.dumps({"index": 2, "reason": "manual"})
        mock_page_event = MockEvent(page_changed_json)
        carousel._on_page_changed_internal(mock_page_event)
        
        # Test scrolled event
        scrolled_json = json.dumps({"offset": 150.5})
        mock_scroll_event = MockEvent(scrolled_json)
        carousel._on_scrolled_internal(mock_scroll_event)
        
        # Verify events were received and decoded properly
        assert len(received_events) == 2
        
        # Check page changed event
        page_event_type, page_event_data = received_events[0]
        assert page_event_type == 'page_changed'
        assert isinstance(page_event_data, dict)
        assert page_event_data['index'] == 2
        assert page_event_data['reason'] == 'manual'
        
        # Check scrolled event
        scroll_event_type, scroll_event_data = received_events[1]
        assert scroll_event_type == 'scrolled'
        assert isinstance(scroll_event_data, dict)
        assert scroll_event_data['offset'] == 150.5
        
        print("✅ JSON event handling test passed - Events properly decoded")
        return True
        
    except Exception as e:
        print(f"❌ JSON event handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test."""
    print("🧪 Testing JSON Event Handling")
    print("=" * 40)
    
    if test_json_event_handling():
        print("\n🎉 Test passed! JSON events are working correctly.")
        return 0
    else:
        print("\n⚠️  Test failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
