#!/usr/bin/env python3

import flet as ft
from src.flet_carousel_slider import FletCarouselSlider, ScrollDirection, CenterPageEnlargeStrategy, AnimationCurve

def main(page: ft.Page):
    page.title = "Flet Carousel Slider Example"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.padding = 20

    # Create carousel items
    carousel_items = []
    colors = [ft.colors.RED, ft.colors.BLUE, ft.colors.GREEN, ft.colors.ORANGE, ft.colors.PURPLE]
    
    for i, color in enumerate(colors):
        item = ft.Container(
            content=ft.Column([
                ft.Text(f"Slide {i+1}", size=24, weight=ft.FontWeight.BOLD, color=ft.colors.WHITE),
                ft.Text(f"This is slide number {i+1}", size=16, color=ft.colors.WHITE),
                ft.Icon(ft.icons.STAR, size=50, color=ft.colors.WHITE),
            ], 
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            bgcolor=color,
            border_radius=10,
            padding=20,
            margin=ft.margin.symmetric(horizontal=5),
        )
        carousel_items.append(item)

    # Current page indicator
    current_page_text = ft.Text("Current page: 0", size=16, weight=ft.FontWeight.BOLD)

    def on_page_changed(e):
        data = e.data
        current_page_text.value = f"Current page: {data.get('index', 0)} (Reason: {data.get('reason', 'unknown')})"
        page.update()

    def on_scrolled(e):
        data = e.data
        print(f"Scroll - Page: {data.get('current_page', 0)}, Offset: {data.get('offset', 0):.2f}")

    # Create carousel slider
    carousel = FletCarouselSlider(
        items=carousel_items,
        height=300,
        auto_play=True,
        auto_play_interval=3000,  # 3 seconds
        auto_play_animation_duration=800,
        auto_play_curve=AnimationCurve.FAST_OUT_SLOWIN,
        enlarge_center_page=True,
        enlarge_factor=0.3,
        enlarge_strategy=CenterPageEnlargeStrategy.SCALE,
        viewport_fraction=0.8,
        enable_infinite_scroll=True,
        enable_scroll_events=True,  # Enable scroll events (optional)
        on_page_changed=on_page_changed,
        on_scrolled=on_scrolled,
    )

    # Control buttons
    def next_page(e):
        carousel.next_page(duration=500, curve=AnimationCurve.EASE_IN_OUT)

    def previous_page(e):
        carousel.previous_page(duration=500, curve=AnimationCurve.EASE_IN_OUT)

    def jump_to_first(e):
        carousel.jump_to_page(0)

    def animate_to_last(e):
        carousel.animate_to_page(len(carousel_items) - 1, duration=1000, curve=AnimationCurve.BOUNCE_OUT)

    def toggle_auto_play(e):
        if e.control.text == "Stop Auto Play":
            carousel.stop_auto_play()
            e.control.text = "Start Auto Play"
        else:
            carousel.start_auto_play()
            e.control.text = "Stop Auto Play"
        page.update()

    # Button controls
    controls_row = ft.Row([
        ft.ElevatedButton("Previous", on_click=previous_page),
        ft.ElevatedButton("Next", on_click=next_page),
        ft.ElevatedButton("Jump to First", on_click=jump_to_first),
        ft.ElevatedButton("Animate to Last", on_click=animate_to_last),
        ft.ElevatedButton("Stop Auto Play", on_click=toggle_auto_play),
    ], alignment=ft.MainAxisAlignment.CENTER)

    # Create a second carousel with different settings
    simple_items = [
        ft.Container(
            content=ft.Text(f"Item {i}", size=20, color=ft.colors.WHITE),
            bgcolor=ft.colors.BLUE_400,
            border_radius=5,
            padding=20,
            alignment=ft.alignment.center,
        ) for i in range(1, 6)
    ]

    simple_carousel = FletCarouselSlider(
        items=simple_items,
        height=150,
        viewport_fraction=0.6,
        enlarge_center_page=False,
        auto_play=False,
        scroll_direction=ScrollDirection.HORIZONTAL,
    )

    # Layout
    page.add(
        ft.Column([
            ft.Text("Flet Carousel Slider Demo", size=28, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            ft.Text("Main Carousel (Auto-play, Enlarge Center)", size=18, weight=ft.FontWeight.W_500),
            carousel,
            current_page_text,
            controls_row,
            ft.Divider(),
            ft.Text("Simple Carousel", size=18, weight=ft.FontWeight.W_500),
            simple_carousel,
        ], spacing=20)
    )

if __name__ == "__main__":
    ft.app(target=main)
