Running package command
Extra PyPi indexes: [https://pypi.flet.dev]
Created temp directory: C:\Users\<USER>\AppData\Local\Temp\serious_python_tempbe7a507
Copying Python app from C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\src to a temp directory
Configured Windows/ platform with sitecustomize.py
Installing [flet-carousel-slider @ file://C:\Users\<USER>\Desktop\flet\app_view\app_view, flet, --no-cache-dir] with pip command to C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\site-packages
Copying Flutter packages to C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter-packages-temp
Cleanup installed packages
Creating app archive at C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter\app/app.zip from a temp directory
Writing app archive hash to C:\Users\<USER>\Desktop\flet\app_view\app_view\examples\flet_carousel_slider_example\build\flutter\app/app.zip.hash
Deleting temp directory
